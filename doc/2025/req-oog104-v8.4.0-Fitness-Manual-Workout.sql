ALTER TABLE `proj_fitness_workout_image`
    ADD COLUMN `workout_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '手组 workout id 集合' AFTER `detail_image`;
ALTER TABLE `proj_fitness_workout_image_pub`
    ADD COLUMN `workout_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '手组 workout id 集合' AFTER `detail_image`;

-- 为 proj_fitness_workout_generate 表添加新字段
ALTER TABLE `proj_fitness_workout_generate`
    ADD COLUMN `abs_rate` int NULL COMMENT 'Main中Abs占Main的比例，存的值为百分比' AFTER `calorie`;
ALTER TABLE `proj_fitness_workout_generate`
    ADD COLUMN `standing_rate` int NULL COMMENT 'Main中Standing占Main的比例，存的值为百分比' AFTER `abs_rate`;

-- 为范围查询字段添加索引，提升查询性能
ALTER TABLE `proj_fitness_workout_generate`
    ADD INDEX `idx_abs_rate` (`abs_rate`);
ALTER TABLE `proj_fitness_workout_generate`
    ADD INDEX `idx_standing_rate` (`standing_rate`);

ALTER TABLE `proj_fitness_manual_workout`
    MODIFY COLUMN `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT 'Exercise Name' AFTER `id`,
    MODIFY COLUMN `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'Cover Image, supports png/webp formats' AFTER `event_name`,
    MODIFY COLUMN `detail_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'Detail Image, supports png/webp formats' AFTER `cover_image`,
    MODIFY COLUMN `age_group` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'Age Groups, comma separated: 1-12, 13-24, etc.' AFTER `detail_image`,
    MODIFY COLUMN `workout_type` int UNSIGNED NULL COMMENT 'Workout Type: 1-Regular Fitness, 2-Wall Pilates, 3-Chair Yoga, 4-Dumbbells, 5-Resistance Band' AFTER `age_group`;

ALTER TABLE `proj_fitness_manual_workout_pub`
    MODIFY COLUMN `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT 'Exercise Name' AFTER `id`,
    MODIFY COLUMN `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'Cover Image, supports png/webp formats' AFTER `event_name`,
    MODIFY COLUMN `detail_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'Detail Image, supports png/webp formats' AFTER `cover_image`,
    MODIFY COLUMN `age_group` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT 'Age Groups, comma separated: 1-12, 13-24, etc.' AFTER `detail_image`,
    MODIFY COLUMN `workout_type` int UNSIGNED NULL COMMENT 'Workout Type: 1-Regular Fitness, 2-Wall Pilates, 3-Chair Yoga, 4-Dumbbells, 5-Resistance Band' AFTER `age_group`;


--104template 添加新字段 data_version,不再运行 add dataversion sql
ALTER TABLE `proj_fitness_template`
    ADD COLUMN `data_version` int NULL DEFAULT NULL COMMENT '数据版本' AFTER `id`;

ALTER TABLE `proj_fitness_template_pub`
    ADD COLUMN `data_version` int NULL DEFAULT NULL COMMENT '数据版本' AFTER `id`;

--初始化原始数据(正式发布需要判断 pub 是重新发布,还是直接修改表的 data version 字段)
BEGIN;
update proj_fitness_template set data_version = 1 where del_flag = 0;
update proj_fitness_template_pub set data_version = 1 where del_flag = 0;
COMMIT;

-- 新增 Fitness Challenge 相关表
CREATE TABLE `proj_fitness_challenge` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `table_code` tinyint DEFAULT NULL COMMENT '表标识',
    `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Challenge Name, maximum 100 characters',
    `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'Event Name, auto-generated',
    `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'Cover Image, supports png/webp formats',
    `detail_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'Detail Image, supports png/webp formats',
    `type` int unsigned NOT NULL COMMENT 'Challenge Type: 1-Weekly Challenge',
    `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'Description, maximum 1000 characters',
    `subscription` tinyint NOT NULL DEFAULT '0' COMMENT 'Subscription required: 0-No, 1-Yes',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT 'Status: 0-Not Enable, 1-Enable, 2-Disable',
    `proj_id` int unsigned NOT NULL COMMENT '项目id',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_proj_id` (`proj_id`),
    KEY `idx_status` (`status`),
    KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness Challenge';

CREATE TABLE `proj_fitness_challenge_pub` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `version` int DEFAULT NULL COMMENT '版本',
    `table_code` tinyint DEFAULT NULL COMMENT '表标识',
    `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Challenge Name, maximum 100 characters',
    `event_name` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'Event Name, auto-generated',
    `cover_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'Cover Image, supports png/webp formats',
    `detail_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'Detail Image, supports png/webp formats',
    `type` int unsigned NOT NULL COMMENT 'Challenge Type: 1-Weekly Challenge',
    `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'Description, maximum 1000 characters',
    `subscription` tinyint NOT NULL DEFAULT '0' COMMENT 'Subscription required: 0-No, 1-Yes',
    `status` tinyint NOT NULL DEFAULT '0' COMMENT 'Status: 0-Not Enable, 1-Enable, 2-Disable',
    `proj_id` int unsigned NOT NULL COMMENT '项目id',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_proj_id` (`proj_id`),
    KEY `idx_status` (`status`),
    KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness Challenge Pub';

-- Challenge 与 Manual Workout 关联表
CREATE TABLE `proj_fitness_challenge_manual_workout_relation` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `proj_fitness_challenge_id` int unsigned NOT NULL COMMENT 'proj_fitness_challenge id',
    `proj_fitness_manual_workout_id` int unsigned NOT NULL COMMENT 'proj_fitness_manual_workout id',
    `sort_no` int NOT NULL DEFAULT '0' COMMENT '排序字段',
    `proj_id` int unsigned NOT NULL COMMENT '项目id',
    `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识 0 未删除，1已删除',
    `create_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
    `create_time` datetime NOT NULL COMMENT '创建时间',
    `update_user` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '修改人',
    `update_time` datetime DEFAULT NULL COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_challenge_id` (`proj_fitness_challenge_id`),
    KEY `idx_workout_id` (`proj_fitness_manual_workout_id`),
    KEY `idx_proj_id` (`proj_id`),
    UNIQUE KEY `uk_challenge_workout` (`proj_fitness_challenge_id`, `proj_fitness_manual_workout_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Fitness Challenge Manual Workout Relation';